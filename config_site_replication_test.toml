# Test configuration for site-based replication feature
# Redis connection URL for the primary node
redis_url = "redis://settlenxt:npci@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to (Rust nodes)
secondary_nodes = ["http://127.0.0.1:50052"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 1024

# Number of secondary nodes to which data should be replicated
replication_factor = 1

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# IMPORTANT: Set to true to enable site replication (async replication required for site replication)
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts
retry_delay_ms = 100

# Maximum age in seconds for a replication batch
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 512

# Maximum number of operations in a batch
max_batch_size = 1000

# Interval in milliseconds to flush batches
batch_flush_interval_ms = 50

# Authentication configuration
auth_enabled = true
auth_username = "testuser"
auth_password = "testpass"
session_duration_secs = 3600

# Write consistency configuration (not used when async_replication = true)
# These settings are only active when async_replication = false
# List of peer Redis nodes for write consistency (when async_replication = false)
# These are direct Redis connections, not Rust nodes
peer_redis_nodes = ["redis://settlenxt:npci@127.0.0.1:6370", "redis://settlenxt:npci@127.0.0.1:6371"]

# Write consistency level: ALL, QUORUM, or ONE (only used when async_replication = false)
write_consistency = "QUORUM"
quorum_value = 2
write_retry_count = 3
peer_redis_pool_size = 64

# Site-based replication configuration
# Enable site-based replication for cross-site data replication
site_replication_enabled = true

# Primary node for other site (will receive replication with skip_replication=false)
# This is the gRPC endpoint of the primary node in the other site
site_primary_node = "http://site3-primary:50053"

# Failover node for other site (used when primary node is unavailable)
# This is the gRPC endpoint of the failover node in the other site
site_failover_node = "http://site3-primary:50054"

# Number of retry attempts for site replication operations
site_replication_retry_count = 3

# Timeout in milliseconds for site replication operations
site_replication_timeout_ms = 5000

# Maximum number of connections per site node in the gRPC client pool
site_replication_pool_size = 256

# Performance tuning parameters
tcp_keepalive_secs = 30
tcp_nodelay = true
concurrency_limit = 64
max_concurrent_streams = 256
chunk_size = 1000
num_shards = 16
worker_threads = 8